#pragma once


#include "CStyle/humangl.hpp"

// Include specific dependency
#include "BaseMenu.hpp"

// Forward declarations
class InstructionsMenuRenderer;
class MouseHandler;
class MenuInput;

class InstructionsMenu : public BaseMenu {
private:
    InstructionsMenuRenderer& renderer;

public:
    InstructionsMenu(InstructionsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~InstructionsMenu() = default;

    // Implement pure virtual methods from BaseMenu
    void initializeButtons() override;
    void render() override;
    MenuAction handleButtonClick(int buttonIndex) override;
};


