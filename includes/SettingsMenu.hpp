#pragma once


#include "CStyle/humangl.hpp"

// Include specific dependency
#include "BaseMenu.hpp"

// Forward declarations
class SettingsMenuRenderer;
class MouseHandler;
class MenuInput;

class SettingsMenu : public BaseMenu {
private:
    SettingsMenuRenderer& renderer;

public:
    SettingsMenu(SettingsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~SettingsMenu() = default;

    // Implement pure virtual methods from BaseMenu
    void initializeButtons() override;
    void render() override;
    MenuAction handleButtonClick(int buttonIndex) override;
};


